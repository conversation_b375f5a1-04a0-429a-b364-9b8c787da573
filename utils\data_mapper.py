"""
数据映射和转换工具模块
处理MySQL查询结果到标准字典格式的转换，支持字段名映射
"""

from typing import Dict, List, Union, Any, Optional
import logging

logger = logging.getLogger(__name__)


# 文档字段映射配置：MySQL字段名 -> 目标字段名
DOC_FIELD_MAPPING = {
    # 基础字段（仅包含DOC_TARGET_FIELDS中的字段）
    'id': 'id',
    'original_id': 'originalId',
    'title': 'title',
    'agent_id': 'agentId',
    'dir_id': 'dirId',
    'pub_user_name': 'pubUserName',
    'pub_time': 'pubTime',
    'version': 'version',
    'tags': 'tags',
    'attaches': 'attaches',
    'word_num': 'wordNum',
    'picture_num': 'pictureNum',
    'link_num': 'linkNum',
    'plain_text': 'docText',        # MySQL: plain_text -> 目标: docText
    'rich_text': 'docHtml',         # MySQL: rich_text -> 目标: docHtml
    'version_status': 'versionStatus',

    # 扩展字段（虚拟字段，需要额外查询获得）
    'id_parent': 'id_parent',           # 虚拟字段，通常为空
    'originalId_parent': 'originalId_parent',  # 虚拟字段，通常为空
    'agentName': 'agentName',           # 虚拟字段，需要通过agent_id查询获得
    'dir_name': 'dir_name',             # 虚拟字段，需要通过dir_id查询获得
    'dir_level': 'dir_level',           # 虚拟字段，需要通过dir_id查询获得
}

# FAQ字段映射配置：MySQL字段名 -> 目标字段名
FAQ_FIELD_MAPPING = {
    # 基础字段（仅包含FAQ_TARGET_FIELDS中的字段）
    'id': 'id',
    'agent_id': 'agentId',
    'question': 'question',
    'answer': 'answer',
    'dir_id': 'dirId',
    'original_id': 'originalId',
    'pub_user_name': 'pubUserName',
    'pub_time': 'pubTime',
    'version_status': 'versionStatus',

    # 扩展字段（虚拟字段，需要额外查询获得）
    'agentName': 'agentName',           # 虚拟字段，需要通过agent_id查询获得
    'dir_name': 'dir_name',             # 虚拟字段，需要通过dir_id查询获得
    'dir_level': 'dir_level',           # 虚拟字段，需要通过dir_id查询获得
}

# 目标字段顺序定义（兼容现有代码的核心字段）
DOC_TARGET_FIELDS = [
    'id', 'originalId', 'id_parent', 'title', 'agentId', 'agentName',
    'dirId', 'originalId_parent', 'pubUserName', 'pubTime', 'version',
    'tags', 'attaches', 'wordNum', 'pictureNum', 'linkNum', 'docText',
    'docHtml', 'dir_name', 'dir_level', 'versionStatus'
]

FAQ_TARGET_FIELDS = [
    'id', 'agentId', 'agentName', 'question', 'answer', 'dirId',
    'originalId', 'pubUserName', 'pubTime', 'versionStatus',
    'dir_name', 'dir_level'
]




class DataMapper:
    """数据映射器类，处理MySQL查询结果到标准格式的转换"""

    def __init__(self):
        """初始化数据映射器"""
        self.doc_mapping = DOC_FIELD_MAPPING
        self.faq_mapping = FAQ_FIELD_MAPPING
        self.doc_fields = DOC_TARGET_FIELDS
        self.faq_fields = FAQ_TARGET_FIELDS

    def map_doc_result(self, mysql_result: Union[Dict, List[Dict], tuple, List[tuple]],
                       use_dict_cursor: bool = True) -> Union[Dict, List[Dict]]:
        """
        映射文档查询结果

        Args:
            mysql_result: MySQL查询结果
            use_dict_cursor: 是否使用DictCursor查询的结果

        Returns:
            映射后的标准格式字典或字典列表
        """
        return self._map_result(
            mysql_result,
            self.doc_mapping,
            DOC_TARGET_FIELDS,
            use_dict_cursor
        )

    def map_faq_result(self, mysql_result: Union[Dict, List[Dict], tuple, List[tuple]],
                       use_dict_cursor: bool = True) -> Union[Dict, List[Dict]]:
        """
        映射FAQ查询结果

        Args:
            mysql_result: MySQL查询结果
            use_dict_cursor: 是否使用DictCursor查询的结果

        Returns:
            映射后的标准格式字典或字典列表
        """
        return self._map_result(
            mysql_result,
            self.faq_mapping,
            FAQ_TARGET_FIELDS,
            use_dict_cursor
        )

    def _map_result(self, mysql_result: Union[Dict, List[Dict], tuple, List[tuple]],
                    field_mapping: Dict[str, str],
                    target_fields: List[str],
                    use_dict_cursor: bool) -> Union[Dict, List[Dict]]:
        """
        内部映射方法

        Args:
            mysql_result: MySQL查询结果
            field_mapping: 字段映射配置
            target_fields: 目标字段列表
            use_dict_cursor: 是否使用DictCursor

        Returns:
            映射后的结果
        """
        if not mysql_result:
            return [] if isinstance(mysql_result, list) else {}

        if use_dict_cursor:
            # 检查是否为字典或字典列表
            if isinstance(mysql_result, dict):
                dict_result = mysql_result
                return self._map_dict_result(dict_result, field_mapping, target_fields)
            elif isinstance(mysql_result, list):
                if not mysql_result or isinstance(mysql_result[0], dict):
                    # 类型忽略：我们已经在运行时检查了类型
                    return self._map_dict_result(mysql_result, field_mapping, target_fields)  # type: ignore
                else:
                    raise TypeError("使用DictCursor时，列表中的元素必须是字典")
            else:
                raise TypeError("使用DictCursor时，mysql_result必须是字典或字典列表")
        else:
            # 检查是否为元组或元组列表
            if isinstance(mysql_result, tuple):
                tuple_result = mysql_result
                return self._map_tuple_result(tuple_result, field_mapping, target_fields)
            elif isinstance(mysql_result, list):
                if not mysql_result or isinstance(mysql_result[0], tuple):
                    # 类型忽略：我们已经在运行时检查了类型
                    return self._map_tuple_result(mysql_result, field_mapping, target_fields)  # type: ignore
                else:
                    raise TypeError("使用传统Cursor时，列表中的元素必须是元组")
            else:
                raise TypeError("使用传统Cursor时，mysql_result必须是元组或元组列表")
    
    def _map_dict_result(self, dict_result: Union[Dict, List[Dict]], 
                         field_mapping: Dict[str, str], 
                         target_fields: List[str]) -> Union[Dict, List[Dict]]:
        """映射DictCursor查询结果"""
        def map_single_dict(source_dict: Dict) -> Dict:
            mapped_dict = {}
            
            # 首先按照映射关系转换字段
            for mysql_field, target_field in field_mapping.items():
                if mysql_field in source_dict:
                    mapped_dict[target_field] = source_dict[mysql_field]
            
            # 然后按照目标字段顺序组织结果，确保所有字段都存在
            ordered_dict = {}
            for field in target_fields:
                ordered_dict[field] = mapped_dict.get(field, None)
            
            return ordered_dict
        
        if isinstance(dict_result, list):
            return [map_single_dict(row) for row in dict_result]
        else:
            return map_single_dict(dict_result)
    
    def _map_tuple_result(self, tuple_result: Union[tuple, List[tuple]], 
                          field_mapping: Dict[str, str], 
                          target_fields: List[str]) -> Union[Dict, List[Dict]]:
        """映射传统Cursor查询结果（tuple格式）"""
        # 获取MySQL字段顺序（需要在SQL查询时保证顺序）
        mysql_fields = list(field_mapping.keys())
        
        def map_single_tuple(row: tuple) -> Dict:
            if len(row) != len(mysql_fields):
                logger.warning(f"字段数量不匹配: 查询结果有{len(row)}个字段，期望{len(mysql_fields)}个字段")
            
            # 先将tuple转换为临时字典
            temp_dict = {}
            for i, mysql_field in enumerate(mysql_fields):
                if i < len(row):
                    target_field = field_mapping[mysql_field]
                    temp_dict[target_field] = row[i]
            
            # 然后按照目标字段顺序组织结果
            ordered_dict = {}
            for field in target_fields:
                ordered_dict[field] = temp_dict.get(field, None)
            
            return ordered_dict
        
        if isinstance(tuple_result, list):
            return [map_single_tuple(row) for row in tuple_result]
        else:
            return map_single_tuple(tuple_result)
    
    def get_doc_sql_fields(self) -> str:
        """
        获取文档查询的SQL字段列表（MySQL字段名）

        Returns:
            SQL字段列表字符串
        """
        return ", ".join(self.doc_mapping.keys())

    def get_faq_sql_fields(self) -> str:
        """
        获取FAQ查询的SQL字段列表（MySQL字段名）

        Returns:
            SQL字段列表字符串
        """
        return ", ".join(self.faq_mapping.keys())


# 创建全局实例
data_mapper = DataMapper()

# 便捷函数
def map_doc_result(mysql_result: Union[Dict, List[Dict], tuple, List[tuple]],
                   use_dict_cursor: bool = True) -> Union[Dict, List[Dict]]:
    """映射文档查询结果的便捷函数"""
    return data_mapper.map_doc_result(mysql_result, use_dict_cursor)

def map_faq_result(mysql_result: Union[Dict, List[Dict], tuple, List[tuple]],
                   use_dict_cursor: bool = True) -> Union[Dict, List[Dict]]:
    """映射FAQ查询结果的便捷函数"""
    return data_mapper.map_faq_result(mysql_result, use_dict_cursor)

def get_doc_sql_fields() -> str:
    """获取文档SQL字段列表的便捷函数（MySQL字段名）"""
    return data_mapper.get_doc_sql_fields()

def get_faq_sql_fields() -> str:
    """获取FAQ SQL字段列表的便捷函数（MySQL字段名）"""
    return data_mapper.get_faq_sql_fields()
